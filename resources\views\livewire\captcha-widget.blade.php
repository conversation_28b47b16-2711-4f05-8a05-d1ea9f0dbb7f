@if($isRequired && $captchaConfig['enabled'])
    <div class="captcha-container" wire:ignore>
        @if($captchaConfig['provider'] === 'turnstile')
            <div id="turnstile-widget-{{ $this->getId() }}" class="cf-turnstile" data-sitekey="{{ $captchaConfig['site_key'] }}"
                data-callback="turnstileCallback{{ $this->getId() }}"
                data-expired-callback="turnstileExpired{{ $this->getId() }}"
                data-error-callback="turnstileError{{ $this->getId() }}" data-theme="auto" data-size="normal"></div>
        @elseif($captchaConfig['provider'] === 'recaptcha')
            <div id="recaptcha-widget-{{ $this->getId() }}" class="g-recaptcha" data-sitekey="{{ $captchaConfig['site_key'] }}"
                data-callback="recaptchaCallback{{ $this->getId() }}"
                data-expired-callback="recaptchaExpired{{ $this->getId() }}"
                data-error-callback="recaptchaError{{ $this->getId() }}" data-theme="light" data-size="normal"></div>
        @endif
    </div>

    @push('scripts')
        @if($captchaConfig['provider'] === 'turnstile')
            <script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>
            <script>
                window.turnstileCallback{{ $this->getId() }} = function (token) {
                    @this.call('captchaCallback', token);
                };

                window.turnstileExpired{{ $this->getId() }} = function () {
                    @this.call('captchaExpired');
                };

                window.turnstileError{{ $this->getId() }} = function () {
                    @this.call('captchaError');
                };
            </script>
        @elseif($captchaConfig['provider'] === 'recaptcha')
            <script src="https://www.google.com/recaptcha/api.js" async defer></script>
            <script>
                window.recaptchaCallback{{ $this->getId() }} = function (token) {
                    @this.call('captchaCallback', token);
                };

                window.recaptchaExpired{{ $this->getId() }} = function () {
                    @this.call('captchaExpired');
                };

                window.recaptchaError{{ $this->getId() }} = function () {
                    @this.call('captchaError');
                };
            </script>
        @endif
    @endpush
@endif