<!--[if BLOCK]><![endif]--><?php if($isRequired && $captchaConfig['enabled']): ?>
    <div class="captcha-container" wire:ignore>
        <!--[if BLOCK]><![endif]--><?php if($captchaConfig['provider'] === 'turnstile'): ?>
            <div id="turnstile-widget-<?php echo e($this->getId()); ?>" class="cf-turnstile" data-sitekey="<?php echo e($captchaConfig['site_key']); ?>"
                data-callback="turnstileCallback<?php echo e($this->getId()); ?>"
                data-expired-callback="turnstileExpired<?php echo e($this->getId()); ?>"
                data-error-callback="turnstileError<?php echo e($this->getId()); ?>" data-theme="auto" data-size="normal"></div>
        <?php elseif($captchaConfig['provider'] === 'recaptcha'): ?>
            <div id="recaptcha-widget-<?php echo e($this->getId()); ?>" class="g-recaptcha" data-sitekey="<?php echo e($captchaConfig['site_key']); ?>"
                data-callback="recaptchaCallback<?php echo e($this->getId()); ?>"
                data-expired-callback="recaptchaExpired<?php echo e($this->getId()); ?>"
                data-error-callback="recaptchaError<?php echo e($this->getId()); ?>" data-theme="light" data-size="normal"></div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>

    <?php $__env->startPush('scripts'); ?>
        <!--[if BLOCK]><![endif]--><?php if($captchaConfig['provider'] === 'turnstile'): ?>
            <script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>
            <script>
                window.turnstileCallback<?php echo e($this->getId()); ?> = function (token) {
                    window.Livewire.find('<?php echo e($_instance->getId()); ?>').call('captchaCallback', token);
                };

                window.turnstileExpired<?php echo e($this->getId()); ?> = function () {
                    window.Livewire.find('<?php echo e($_instance->getId()); ?>').call('captchaExpired');
                };

                window.turnstileError<?php echo e($this->getId()); ?> = function () {
                    window.Livewire.find('<?php echo e($_instance->getId()); ?>').call('captchaError');
                };
            </script>
        <?php elseif($captchaConfig['provider'] === 'recaptcha'): ?>
            <script src="https://www.google.com/recaptcha/api.js" async defer></script>
            <script>
                window.recaptchaCallback<?php echo e($this->getId()); ?> = function (token) {
                    window.Livewire.find('<?php echo e($_instance->getId()); ?>').call('captchaCallback', token);
                };

                window.recaptchaExpired<?php echo e($this->getId()); ?> = function () {
                    window.Livewire.find('<?php echo e($_instance->getId()); ?>').call('captchaExpired');
                };

                window.recaptchaError<?php echo e($this->getId()); ?> = function () {
                    window.Livewire.find('<?php echo e($_instance->getId()); ?>').call('captchaError');
                };
            </script>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    <?php $__env->stopPush(); ?>
<?php endif; ?><!--[if ENDBLOCK]><![endif]--><?php /**PATH C:\Users\<USER>\Herd\graf\resources\views/livewire/captcha-widget.blade.php ENDPATH**/ ?>