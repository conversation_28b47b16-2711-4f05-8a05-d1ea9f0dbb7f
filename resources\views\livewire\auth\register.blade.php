<?php

use App\Models\User;
use App\Services\CaptchaService;
use App\Services\PlatformConfigService;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Validate;
use Livewire\Volt\Component;

new #[Layout('components.layouts.auth')] class extends Component {
    #[Validate('required|string|max:255|regex:/^[a-zA-Z\s\-\'\.]+$/')]
    public string $first_name = '';

    #[Validate('required|string|max:255|regex:/^[a-zA-Z\s\-\'\.]+$/')]
    public string $last_name = '';

    public string $username = '';

    #[Validate('required|string|lowercase|email:rfc,dns|max:255')]
    public string $email = '';

    #[Validate('required|string|confirmed')]
    public string $password = '';

    public string $password_confirmation = '';

    #[Validate('required|accepted')]
    public bool $terms_accepted = false;

    public string $captcha_response = '';

    public bool $showPassword = false;
    public bool $showPasswordConfirmation = false;

    /**
     * Get validation rules
     */
    public function rules(): array
    {
        $usernameMin = PlatformConfigService::usernameMinLength();
        $usernameMax = PlatformConfigService::usernameMaxLength();

        $rules = [
            'first_name' => [
                'required',
                'string',
                'max:255',
                'regex:/^[a-zA-Z\s\-\'\.]+$/',
            ],
            'last_name' => [
                'required',
                'string',
                'max:255',
                'regex:/^[a-zA-Z\s\-\'\.]+$/',
            ],
            'username' => [
                'required',
                'string',
                "min:{$usernameMin}",
                "max:{$usernameMax}",
                'regex:/^[a-zA-Z0-9_]+$/',
                'unique:' . User::class,
            ],
            'email' => [
                'required',
                'string',
                'lowercase',
                'email:rfc,dns',
                'max:255',
                'unique:' . User::class,
            ],
            'password' => [
                'required',
                'string',
                'confirmed',
                Rules\Password::defaults(),
            ],
            'terms_accepted' => [
                'required',
                'accepted',
            ],
        ];

        // Add captcha validation if required
        if (CaptchaService::isRequired()) {
            $rules['captcha_response'] = [
                'required',
                function ($attribute, $value, $fail) {
                    if (!CaptchaService::verify($value)) {
                        $fail('Please complete the captcha verification.');
                    }
                },
            ];
        }

        return $rules;
    }

    /**
     * Handle captcha completion
     */
    public function captchaCompleted($response): void
    {
        $this->captcha_response = $response;
    }

    /**
     * Handle captcha expiration
     */
    public function captchaExpired(): void
    {
        $this->captcha_response = '';
    }

    /**
     * Handle an incoming registration request.
     */
    public function register(): void
    {
        $validated = $this->validate();

        // Hash the password
        $validated['password'] = Hash::make($validated['password']);

        // Set terms acceptance timestamp
        $validated['terms_accepted_at'] = now();

        // Remove captcha response from user data
        unset($validated['captcha_response'], $validated['terms_accepted']);

        event(new Registered(($user = User::create($validated))));

        Auth::login($user);

        $this->redirectIntended(route('dashboard', absolute: false), navigate: true);
    }
}; ?>

<div class="flex flex-col gap-6">
    <x-auth-header
        :title="__('Join :platform', ['platform' => config('platform.name', 'Graf')])"
        :description="__('Create your account to start analyzing YouTube data')"
    />

    <!-- Session Status -->
    <x-auth-session-status class="text-center" :status="session('status')" />

    <form method="POST" wire:submit="register" class="flex flex-col gap-6">
        <!-- Name Fields Row -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- First Name -->
            <flux:input
                wire:model.blur="first_name"
                :label="__('First name')"
                type="text"
                required
                autofocus
                autocomplete="given-name"
                :placeholder="__('First name')"
            />

            <!-- Last Name -->
            <flux:input
                wire:model.blur="last_name"
                :label="__('Last name')"
                type="text"
                required
                autocomplete="family-name"
                :placeholder="__('Last name')"
            />
        </div>

        <!-- Username -->
        <flux:input
            wire:model.blur="username"
            :label="__('Username')"
            type="text"
            required
            autocomplete="username"
            :placeholder="__('Choose a unique username')"
            :description="__('Between :min and :max characters. Letters, numbers, and underscores only.', [
                'min' => config('platform.auth.registration.username_min_length', 3),
                'max' => config('platform.auth.registration.username_max_length', 30)
            ])"
        />

        <!-- Email Address -->
        <flux:input
            wire:model.blur="email"
            :label="__('Email address')"
            type="email"
            required
            autocomplete="email"
            placeholder="<EMAIL>"
            :description="__('We\'ll use this to send you important updates')"
        />

        <!-- Password Fields Row -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Password -->
            <flux:input
                wire:model="password"
                :label="__('Password')"
                type="password"
                required
                autocomplete="new-password"
                :placeholder="__('Create a strong password')"
                viewable
            />

            <!-- Confirm Password -->
            <flux:input
                wire:model="password_confirmation"
                :label="__('Confirm password')"
                type="password"
                required
                autocomplete="new-password"
                :placeholder="__('Confirm your password')"
                viewable
            />
        </div>

        <!-- Captcha -->
        @if(App\Services\CaptchaService::isRequired())
        <div class="space-y-2">
            <flux:label>{{ __('Security verification') }}</flux:label>
            <livewire:captcha-widget
                wire:key="register-captcha"
                @captcha-completed="captchaCompleted($event.detail)"
                @captcha-expired="captchaExpired"
            />
            @error('captcha_response')
                <flux:error>{{ $message }}</flux:error>
            @enderror
        </div>
        @endif

        <!-- Terms Acceptance -->
        <div class="space-y-3">
            <flux:checkbox
                wire:model="terms_accepted"
                :label="__('I agree to the :terms and :privacy', [
                    'terms' => '<a href=\'' . config('platform.legal.terms_of_service', '/terms') . '\' target=\'_blank\' class=\'text-blue-600 hover:text-blue-800 underline\'>Terms of Service</a>',
                    'privacy' => '<a href=\'' . config('platform.legal.privacy_policy', '/privacy') . '\' target=\'_blank\' class=\'text-blue-600 hover:text-blue-800 underline\'>Privacy Policy</a>'
                ])"
                required
            />
            @error('terms_accepted')
                <flux:error>{{ $message }}</flux:error>
            @enderror
        </div>

        <div class="flex items-center justify-end">
            <flux:button type="submit" variant="primary" class="w-full">
                {{ __('Create account') }}
            </flux:button>
        </div>
    </form>

    <div class="space-x-1 rtl:space-x-reverse text-center text-sm text-zinc-600 dark:text-zinc-400">
        <span>{{ __('Already have an account?') }}</span>
        <flux:link :href="route('login')" wire:navigate>{{ __('Sign in') }}</flux:link>
    </div>
</div>
