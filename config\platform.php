<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Platform Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options specific to the Graf platform,
    | including branding, features, and service integrations.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Platform Identity
    |--------------------------------------------------------------------------
    |
    | These values define the platform's identity and branding information
    | that will be used throughout the application.
    |
    */

    'name' => env('APP_NAME', 'Graf'),
    'description' => env('APP_DESCRIPTION', 'YouTube Analytics and Insights Platform'),
    'tagline' => env('APP_TAGLINE', 'Unlock the power of YouTube data'),

    /*
    |--------------------------------------------------------------------------
    | Supported Platforms
    |--------------------------------------------------------------------------
    |
    | Define which social media platforms are supported by the application.
    | YouTube is the primary platform, with Twitch and TikTok planned.
    |
    */

    'supported_platforms' => [
        'youtube' => [
            'name' => 'YouTube',
            'enabled' => true,
            'api_key' => env('YOUTUBE_API_KEY'),
            'features' => [
                'channel_analytics',
                'video_insights',
                'creator_tools',
            ],
        ],
        'twitch' => [
            'name' => 'Twitch',
            'enabled' => false, // Coming soon
            'api_key' => env('TWITCH_API_KEY'),
            'features' => [
                'stream_analytics',
                'viewer_insights',
            ],
        ],
        'tiktok' => [
            'name' => 'TikTok',
            'enabled' => false, // Coming soon
            'api_key' => env('TIKTOK_API_KEY'),
            'features' => [
                'video_analytics',
                'trend_analysis',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Authentication Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration options for the authentication system including
    | registration requirements and security settings.
    |
    */

    'auth' => [
        'registration' => [
            'enabled' => env('REGISTRATION_ENABLED', true),
            'requires_email_verification' => env('REGISTRATION_REQUIRES_EMAIL_VERIFICATION', true),
            'requires_terms_acceptance' => env('REGISTRATION_REQUIRES_TERMS', true),
            'requires_captcha' => env('REGISTRATION_REQUIRES_CAPTCHA', true),
            'username_min_length' => env('USERNAME_MIN_LENGTH', 3),
            'username_max_length' => env('USERNAME_MAX_LENGTH', 30),
        ],
        'captcha' => [
            'primary_provider' => env('CAPTCHA_PRIMARY_PROVIDER', 'turnstile'),
            'fallback_provider' => env('CAPTCHA_FALLBACK_PROVIDER', 'recaptcha'),
            'providers' => [
                'turnstile' => [
                    'site_key' => env('TURNSTILE_SITE_KEY'),
                    'secret_key' => env('TURNSTILE_SECRET_KEY'),
                    'enabled' => env('TURNSTILE_ENABLED', true),
                ],
                'recaptcha' => [
                    'site_key' => env('RECAPTCHA_SITE_KEY'),
                    'secret_key' => env('RECAPTCHA_SECRET_KEY'),
                    'enabled' => env('RECAPTCHA_ENABLED', true),
                    'version' => env('RECAPTCHA_VERSION', 'v2'),
                ],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Legal Pages
    |--------------------------------------------------------------------------
    |
    | URLs or routes for legal pages that users need to access during
    | registration and throughout their use of the platform.
    |
    */

    'legal' => [
        'terms_of_service' => env('TERMS_OF_SERVICE_URL', '/terms'),
        'privacy_policy' => env('PRIVACY_POLICY_URL', '/privacy'),
        'cookie_policy' => env('COOKIE_POLICY_URL', '/cookies'),
        'data_deletion_request' => env('DATA_DELETION_URL', '/data-deletion'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Data Retention
    |--------------------------------------------------------------------------
    |
    | Configuration for data retention and user data management policies.
    |
    */

    'data_retention' => [
        'user_data_retention_days' => env('USER_DATA_RETENTION_DAYS', 2555), // ~7 years
        'analytics_data_retention_days' => env('ANALYTICS_DATA_RETENTION_DAYS', 1095), // 3 years
        'allow_data_deletion_requests' => env('ALLOW_DATA_DELETION_REQUESTS', true),
    ],

];
