<?php

namespace App\Services;

use Illuminate\Support\Facades\Config;

class PlatformConfigService
{
    /**
     * Get the platform name
     */
    public static function name(): string
    {
        return Config::get('platform.name', 'Graf');
    }

    /**
     * Get the platform description
     */
    public static function description(): string
    {
        return Config::get('platform.description', 'YouTube Analytics and Insights Platform');
    }

    /**
     * Get the platform tagline
     */
    public static function tagline(): string
    {
        return Config::get('platform.tagline', 'Unlock the power of YouTube data');
    }

    /**
     * Check if registration is enabled
     */
    public static function isRegistrationEnabled(): bool
    {
        return Config::get('platform.auth.registration.enabled', true);
    }

    /**
     * Check if email verification is required
     */
    public static function requiresEmailVerification(): bool
    {
        return Config::get('platform.auth.registration.requires_email_verification', true);
    }

    /**
     * Check if terms acceptance is required
     */
    public static function requiresTermsAcceptance(): bool
    {
        return Config::get('platform.auth.registration.requires_terms_acceptance', true);
    }

    /**
     * Check if captcha is required
     */
    public static function requiresCaptcha(): bool
    {
        return Config::get('platform.auth.registration.requires_captcha', true);
    }

    /**
     * Get username minimum length
     */
    public static function usernameMinLength(): int
    {
        return Config::get('platform.auth.registration.username_min_length', 3);
    }

    /**
     * Get username maximum length
     */
    public static function usernameMaxLength(): int
    {
        return Config::get('platform.auth.registration.username_max_length', 30);
    }

    /**
     * Get primary captcha provider
     */
    public static function primaryCaptchaProvider(): string
    {
        return Config::get('platform.auth.captcha.primary_provider', 'turnstile');
    }

    /**
     * Get fallback captcha provider
     */
    public static function fallbackCaptchaProvider(): string
    {
        return Config::get('platform.auth.captcha.fallback_provider', 'recaptcha');
    }

    /**
     * Get captcha provider configuration
     */
    public static function captchaProvider(string $provider): array
    {
        return Config::get("platform.auth.captcha.providers.{$provider}", []);
    }

    /**
     * Get active captcha provider configuration
     */
    public static function activeCaptchaProvider(): array
    {
        $primary = static::primaryCaptchaProvider();
        $config = static::captchaProvider($primary);
        
        // If primary provider is not properly configured, try fallback
        if (!$config['enabled'] ?? false || empty($config['site_key'] ?? '')) {
            $fallback = static::fallbackCaptchaProvider();
            $fallbackConfig = static::captchaProvider($fallback);
            
            if ($fallbackConfig['enabled'] ?? false && !empty($fallbackConfig['site_key'] ?? '')) {
                return array_merge($fallbackConfig, ['provider' => $fallback]);
            }
        }
        
        return array_merge($config, ['provider' => $primary]);
    }

    /**
     * Get terms of service URL
     */
    public static function termsOfServiceUrl(): string
    {
        return Config::get('platform.legal.terms_of_service', '/terms');
    }

    /**
     * Get privacy policy URL
     */
    public static function privacyPolicyUrl(): string
    {
        return Config::get('platform.legal.privacy_policy', '/privacy');
    }

    /**
     * Get supported platforms
     */
    public static function supportedPlatforms(): array
    {
        return Config::get('platform.supported_platforms', []);
    }

    /**
     * Get enabled platforms
     */
    public static function enabledPlatforms(): array
    {
        return collect(static::supportedPlatforms())
            ->filter(fn($platform) => $platform['enabled'] ?? false)
            ->toArray();
    }

    /**
     * Check if a platform is enabled
     */
    public static function isPlatformEnabled(string $platform): bool
    {
        return Config::get("platform.supported_platforms.{$platform}.enabled", false);
    }
}
