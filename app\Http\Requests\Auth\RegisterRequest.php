<?php

namespace App\Http\Requests\Auth;

use App\Models\User;
use App\Services\PlatformConfigService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules;

class RegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return PlatformConfigService::isRegistrationEnabled();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $usernameMin = PlatformConfigService::usernameMinLength();
        $usernameMax = PlatformConfigService::usernameMaxLength();

        return [
            'first_name' => [
                'required',
                'string',
                'max:255',
                'regex:/^[a-zA-Z\s\-\'\.]+$/', // Allow letters, spaces, hyphens, apostrophes, dots
            ],
            'last_name' => [
                'required',
                'string',
                'max:255',
                'regex:/^[a-zA-Z\s\-\'\.]+$/', // Allow letters, spaces, hyphens, apostrophes, dots
            ],
            'username' => [
                'required',
                'string',
                "min:{$usernameMin}",
                "max:{$usernameMax}",
                'regex:/^[a-zA-Z0-9_]+$/', // Only alphanumeric and underscores
                'unique:' . User::class,
            ],
            'email' => [
                'required',
                'string',
                'lowercase',
                'email:rfc,dns',
                'max:255',
                'unique:' . User::class,
            ],
            'password' => [
                'required',
                'string',
                'confirmed',
                Rules\Password::defaults(),
            ],
            'terms_accepted' => [
                'required',
                'accepted',
            ],
        ];
    }

    /**
     * Get custom validation messages.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'first_name.required' => 'Please enter your first name.',
            'first_name.regex' => 'First name can only contain letters, spaces, hyphens, apostrophes, and dots.',
            'last_name.required' => 'Please enter your last name.',
            'last_name.regex' => 'Last name can only contain letters, spaces, hyphens, apostrophes, and dots.',
            'username.required' => 'Please choose a username.',
            'username.min' => 'Username must be at least :min characters.',
            'username.max' => 'Username cannot be longer than :max characters.',
            'username.regex' => 'Username can only contain letters, numbers, and underscores.',
            'username.unique' => 'This username is already taken.',
            'email.required' => 'Please enter your email address.',
            'email.email' => 'Please enter a valid email address.',
            'email.unique' => 'An account with this email already exists.',
            'password.required' => 'Please enter a password.',
            'password.confirmed' => 'Password confirmation does not match.',
            'terms_accepted.required' => 'You must accept the terms of service to continue.',
            'terms_accepted.accepted' => 'You must accept the terms of service to continue.',
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'first_name' => 'first name',
            'last_name' => 'last name',
            'username' => 'username',
            'email' => 'email address',
            'password' => 'password',
            'password_confirmation' => 'password confirmation',
            'terms_accepted' => 'terms of service',
        ];
    }
}
