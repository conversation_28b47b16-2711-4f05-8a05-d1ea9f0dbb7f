[2025-09-23 05:46:33] local.ERROR: Livewire encountered a missing root tag when trying to render a component. 
 When rendering a Blade view, make sure it contains a root HTML tag. {"exception":"[object] (Livewire\\Exceptions\\RootTagMissingFromViewException(code: 0): Livewire encountered a missing root tag when trying to render a component. 
 When rendering a Blade view, make sure it contains a root HTML tag. at C:\\Users\\<USER>\\Herd\\graf\\vendor\\livewire\\livewire\\src\\Drawer\\Utils.php:20)
[stacktrace]
#0 C:\\Users\\<USER>\\Herd\\graf\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(266): Livewire\\Drawer\\Utils::insertAttributesIntoHtmlRoot('<!--[if BLOCK]>...', Array)
#1 C:\\Users\\<USER>\\Herd\\graf\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(303): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->{closure:Livewire\\Mechanisms\\HandleComponents\\HandleComponents::render():251}()
#2 C:\\Users\\<USER>\\Herd\\graf\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(251): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Livewire\\CaptchaWidget), Object(Closure))
#3 C:\\Users\\<USER>\\Herd\\graf\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Livewire\\CaptchaWidget), '<div></div>')
#4 C:\\Users\\<USER>\\Herd\\graf\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('captcha-widget', Array, 'register-captch...')
#5 C:\\Users\\<USER>\\Herd\\graf\\vendor\\livewire\\volt\\src\\LivewireManager.php(23): Livewire\\LivewireManager->mount('captcha-widget', Array, 'register-captch...')
#6 C:\\Users\\<USER>\\Herd\\graf\\storage\\framework\\views\\2299e41fc0937019fcb7b2c51c7d4cae.php(233): Livewire\\Volt\\LivewireManager->mount('captcha-widget', Array, 'register-captch...', Array, Array)
#7 C:\\Users\\<USER>\\Herd\\graf\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Users\\\\<USER>\\\\...')
#8 C:\\Users\\<USER>\\Herd\\graf\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): Livewire\\Volt\\Component@anonymous->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()
#9 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#10 C:\\Users\\<USER>\\Herd\\graf\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#11 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#12 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#13 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#14 C:\\Users\\<USER>\\Herd\\graf\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(259): Illuminate\\View\\View->render(Object(Closure))
#15 C:\\Users\\<USER>\\Herd\\graf\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(303): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->{closure:Livewire\\Mechanisms\\HandleComponents\\HandleComponents::render():251}()
#16 C:\\Users\\<USER>\\Herd\\graf\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(251): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(Livewire\\Volt\\Component@anonymous), Object(Closure))
#17 C:\\Users\\<USER>\\Herd\\graf\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(Livewire\\Volt\\Component@anonymous), '<div></div>')
#18 C:\\Users\\<USER>\\Herd\\graf\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('Livewire\\\\Volt\\\\C...', Array, NULL)
#19 C:\\Users\\<USER>\\Herd\\graf\\vendor\\livewire\\volt\\src\\LivewireManager.php(23): Livewire\\LivewireManager->mount('Livewire\\\\Volt\\\\C...', Array, NULL)
#20 C:\\Users\\<USER>\\Herd\\graf\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\Volt\\LivewireManager->mount('Livewire\\\\Volt\\\\C...', Array)
#21 C:\\Users\\<USER>\\Herd\\graf\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->{closure:Livewire\\Features\\SupportPageComponents\\HandlesPageComponents::__invoke():14}()
#22 C:\\Users\\<USER>\\Herd\\graf\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#23 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Livewire\\Component->__invoke()
#24 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#25 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#26 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#27 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(836): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#28 C:\\Users\\<USER>\\Herd\\graf\\vendor\\livewire\\volt\\src\\VoltManager.php(37): Illuminate\\Container\\Container->call(Array)
#29 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php(39): Livewire\\Volt\\VoltManager->{closure:Livewire\\Volt\\VoltManager::route():34}()
#30 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(243): Illuminate\\Routing\\CallableDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Closure))
#31 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(214): Illuminate\\Routing\\Route->runCallable()
#32 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#33 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():821}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated.php(35): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\boost\\src\\Middleware\\InjectBoost.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Laravel\\Boost\\Middleware\\InjectBoost->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#46 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#55 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Herd\\graf\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#69 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#71 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#73 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#75 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#77 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#79 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#80 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#81 C:\\Users\\<USER>\\Herd\\graf\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#82 C:\\Users\\<USER>\\Herd\\graf\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#83 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('C:\\\\Users\\\\<USER>\\\\...')
#84 {main}
"} 
