<?php

namespace App\Services;

use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CaptchaService
{
    /**
     * Verify captcha response
     */
    public static function verify(string $response, string $provider = null): bool
    {
        if (empty($response)) {
            return false;
        }

        $provider = $provider ?: PlatformConfigService::primaryCaptchaProvider();
        $config = PlatformConfigService::captchaProvider($provider);

        if (!($config['enabled'] ?? false) || empty($config['secret_key'] ?? '')) {
            // Try fallback provider
            $fallbackProvider = PlatformConfigService::fallbackCaptchaProvider();
            $fallbackConfig = PlatformConfigService::captchaProvider($fallbackProvider);
            
            if ($fallbackConfig['enabled'] ?? false && !empty($fallbackConfig['secret_key'] ?? '')) {
                return static::verifyWithProvider($response, $fallbackProvider, $fallbackConfig);
            }
            
            Log::warning('No captcha provider configured properly');
            return false;
        }

        return static::verifyWithProvider($response, $provider, $config);
    }

    /**
     * Verify captcha with specific provider
     */
    protected static function verifyWithProvider(string $response, string $provider, array $config): bool
    {
        try {
            switch ($provider) {
                case 'turnstile':
                    return static::verifyTurnstile($response, $config);
                case 'recaptcha':
                    return static::verifyRecaptcha($response, $config);
                default:
                    Log::warning("Unknown captcha provider: {$provider}");
                    return false;
            }
        } catch (\Exception $e) {
            Log::error("Captcha verification failed for provider {$provider}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Verify Cloudflare Turnstile
     */
    protected static function verifyTurnstile(string $response, array $config): bool
    {
        $httpResponse = Http::asForm()->post('https://challenges.cloudflare.com/turnstile/v0/siteverify', [
            'secret' => $config['secret_key'],
            'response' => $response,
            'remoteip' => request()->ip(),
        ]);

        if (!$httpResponse->successful()) {
            Log::error('Turnstile API request failed', [
                'status' => $httpResponse->status(),
                'body' => $httpResponse->body(),
            ]);
            return false;
        }

        $data = $httpResponse->json();
        
        if (!($data['success'] ?? false)) {
            Log::warning('Turnstile verification failed', [
                'error_codes' => $data['error-codes'] ?? [],
            ]);
            return false;
        }

        return true;
    }

    /**
     * Verify Google reCAPTCHA
     */
    protected static function verifyRecaptcha(string $response, array $config): bool
    {
        $httpResponse = Http::asForm()->post('https://www.google.com/recaptcha/api/siteverify', [
            'secret' => $config['secret_key'],
            'response' => $response,
            'remoteip' => request()->ip(),
        ]);

        if (!$httpResponse->successful()) {
            Log::error('reCAPTCHA API request failed', [
                'status' => $httpResponse->status(),
                'body' => $httpResponse->body(),
            ]);
            return false;
        }

        $data = $httpResponse->json();
        
        if (!($data['success'] ?? false)) {
            Log::warning('reCAPTCHA verification failed', [
                'error_codes' => $data['error-codes'] ?? [],
            ]);
            return false;
        }

        // For reCAPTCHA v3, check score if available
        if (isset($data['score'])) {
            $minScore = 0.5; // Configurable threshold
            if ($data['score'] < $minScore) {
                Log::warning('reCAPTCHA score too low', [
                    'score' => $data['score'],
                    'min_score' => $minScore,
                ]);
                return false;
            }
        }

        return true;
    }

    /**
     * Get the active captcha provider configuration for frontend
     */
    public static function getActiveCaptchaConfig(): array
    {
        $config = PlatformConfigService::activeCaptchaProvider();
        
        return [
            'provider' => $config['provider'] ?? 'turnstile',
            'site_key' => $config['site_key'] ?? '',
            'enabled' => $config['enabled'] ?? false,
            'version' => $config['version'] ?? 'v2', // For reCAPTCHA
        ];
    }

    /**
     * Check if captcha is required for registration
     */
    public static function isRequired(): bool
    {
        return PlatformConfigService::requiresCaptcha();
    }

    /**
     * Get captcha validation rule
     */
    public static function getValidationRule(): array
    {
        if (!static::isRequired()) {
            return [];
        }

        return [
            'captcha_response' => [
                'required',
                function ($attribute, $value, $fail) {
                    if (!static::verify($value)) {
                        $fail('Please complete the captcha verification.');
                    }
                },
            ],
        ];
    }
}
