<?php

namespace App\Livewire;

use App\Services\CaptchaService;
use Livewire\Component;

class CaptchaWidget extends Component
{
    public string $captchaResponse = '';
    public array $captchaConfig = [];
    public bool $captchaLoaded = false;

    public function mount()
    {
        $this->captchaConfig = CaptchaService::getActiveCaptchaConfig();
    }

    public function captchaCallback($response)
    {
        $this->captchaResponse = $response;
        $this->dispatch('captcha-completed', $response);
    }

    public function captchaExpired()
    {
        $this->captchaResponse = '';
        $this->dispatch('captcha-expired');
    }

    public function captchaError()
    {
        $this->captchaResponse = '';
        $this->dispatch('captcha-error');
    }

    public function render()
    {
        return view('livewire.captcha-widget', [
            'isRequired' => CaptchaService::isRequired(),
        ]);
    }
}
